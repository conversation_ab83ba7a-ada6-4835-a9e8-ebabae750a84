"""
MySQL数据库连接管理模块
"""
from typing import Dict, List, Optional, Any, Union
import pandas as pd
from sqlalchemy import create_engine, text, Engine
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import QueuePool
from contextlib import contextmanager

from config.settings import settings
from utils.logger import get_logger

logger = get_logger("mysql_client")


class MySQLClient:
    """MySQL数据库客户端"""
    
    def __init__(self):
        self._engine: Optional[Engine] = None
        self._session_factory: Optional[sessionmaker] = None
        self._initialized = False
    
    def initialize(self) -> None:
        """初始化数据库连接"""
        if self._initialized:
            return

        try:
            logger.info(f"正在初始化MySQL连接: {settings.mysql_host}:{settings.mysql_port}")

            # 创建数据库引擎
            self._engine = create_engine(
                settings.mysql_url,
                poolclass=QueuePool,
                pool_size=settings.mysql_pool_size,
                max_overflow=settings.mysql_max_overflow,
                pool_timeout=settings.mysql_pool_timeout,
                pool_pre_ping=True,  # 连接前检查
                echo=settings.debug  # 开发环境下打印SQL
            )

            # 创建会话工厂
            self._session_factory = sessionmaker(
                bind=self._engine,
                autocommit=False,
                autoflush=False
            )

            # 简单测试连接（不调用test_connection避免递归）
            with self._engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()

            self._initialized = True
            logger.info("MySQL数据库连接初始化成功")

        except Exception as e:
            logger.error(f"MySQL数据库连接初始化失败: {e}")
            # 重置状态
            self._engine = None
            self._session_factory = None
            self._initialized = False
            # 不抛出异常，允许系统继续运行
            logger.warning("MySQL连接失败，但系统将继续运行（部分功能可能受限）")
    
    def test_connection(self) -> bool:
        """测试数据库连接"""
        try:
            # 检查引擎是否已初始化
            if self._engine is None:
                logger.warning("MySQL引擎未初始化，尝试重新初始化...")
                self.initialize()

            if self._engine is None:
                logger.error("MySQL引擎初始化失败")
                return False

            with self._engine.connect() as conn:
                result = conn.execute(text("SELECT 1"))
                result.fetchone()
            logger.info("MySQL数据库连接测试成功")
            return True
        except Exception as e:
            logger.error(f"MySQL数据库连接测试失败: {e}")
            return False
    
    @contextmanager
    def get_session(self):
        """获取数据库会话上下文管理器"""
        if not self._initialized:
            self.initialize()
        
        session = self._session_factory()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"数据库会话执行失败: {e}")
            raise
        finally:
            session.close()
    
    def execute_query(
        self, 
        query: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """执行查询并返回结果"""
        if not self._initialized:
            self.initialize()
        
        try:
            with self._engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                columns = result.keys()
                rows = result.fetchall()
                
                # 转换为字典列表
                return [dict(zip(columns, row)) for row in rows]
                
        except Exception as e:
            logger.error(f"查询执行失败: {query}, 错误: {e}")
            raise
    
    def execute_query_to_df(
        self, 
        query: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> pd.DataFrame:
        """执行查询并返回DataFrame"""
        if not self._initialized:
            self.initialize()
        
        try:
            return pd.read_sql(
                sql=text(query),
                con=self._engine,
                params=params or {}
            )
        except Exception as e:
            logger.error(f"查询执行失败: {query}, 错误: {e}")
            raise
    
    def execute_update(
        self, 
        query: str, 
        params: Optional[Dict[str, Any]] = None
    ) -> int:
        """执行更新操作并返回影响行数"""
        if not self._initialized:
            self.initialize()
        
        try:
            with self._engine.connect() as conn:
                result = conn.execute(text(query), params or {})
                conn.commit()
                return result.rowcount
        except Exception as e:
            logger.error(f"更新执行失败: {query}, 错误: {e}")
            raise
    
    def get_table_info(self, table_name: str) -> List[Dict[str, Any]]:
        """获取表结构信息"""
        query = """
        SELECT 
            COLUMN_NAME as column_name,
            DATA_TYPE as data_type,
            IS_NULLABLE as is_nullable,
            COLUMN_DEFAULT as column_default,
            COLUMN_COMMENT as column_comment
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = :database_name 
        AND TABLE_NAME = :table_name
        ORDER BY ORDINAL_POSITION
        """
        
        params = {
            "database_name": settings.mysql_database,
            "table_name": table_name
        }
        
        return self.execute_query(query, params)
    
    def get_all_tables(self) -> List[str]:
        """获取所有表名"""
        query = """
        SELECT TABLE_NAME 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = :database_name
        AND TABLE_TYPE = 'BASE TABLE'
        ORDER BY TABLE_NAME
        """
        
        params = {"database_name": settings.mysql_database}
        result = self.execute_query(query, params)
        return [row["TABLE_NAME"] for row in result]
    
    def close(self) -> None:
        """关闭数据库连接"""
        if self._engine:
            self._engine.dispose()
            logger.info("MySQL数据库连接已关闭")


# 全局MySQL客户端实例
mysql_client = MySQLClient()
