# 股票分析RAG系统

基于RAG（检索增强生成）和Text2SQL技术的智能股票分析系统，提供自然语言查询、语义搜索和深度分析功能。

## 🚀 功能特性

### 核心功能
- **Text2SQL智能查询**: 使用自然语言查询股票数据，自动生成SQL并执行
- **RAG语义搜索**: 基于向量数据库的智能搜索，理解查询意图
- **股票深度分析**: 综合分析、技术分析、基本面分析、风险评估
- **多维度数据**: 价格数据、技术指标、基本面数据、资金流向

### 技术架构
- **前端**: Bootstrap + JavaScript (响应式Web界面)
- **后端**: FastAPI + Python (RESTful API)
- **数据库**: MySQL (结构化数据) + ChromaDB (向量数据)
- **AI模型**: Ollama (本地LLM) + Sentence Transformers (向量化)

## 📋 系统要求

### 硬件要求
- CPU: 4核心以上
- 内存: 8GB以上 (推荐16GB)
- 存储: 10GB可用空间
- 网络: 稳定的互联网连接

### 软件要求
- Python 3.8+
- MySQL 5.7+
- Ollama (用于本地LLM)

## 🛠️ 安装部署

### 1. 克隆项目
```bash
git clone <repository-url>
cd llm-rag
```

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置环境
复制并编辑配置文件：
```bash
cp .env.example .env
```

编辑 `.env` 文件，配置数据库连接等信息：
```env
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_USER=your_username
MYSQL_PASSWORD=your_password
MYSQL_DATABASE=stock_analysis

# Ollama配置
OLLAMA_BASE_URL=http://localhost:11434

# ChromaDB配置
CHROMA_PERSIST_DIRECTORY=./chroma_db
```

### 4. 初始化数据库
确保MySQL服务运行，并创建相应的数据库和表结构。

### 5. 启动Ollama服务
```bash
# 安装并启动Ollama
ollama serve

# 下载所需模型
ollama pull qwen2.5:7b
ollama pull bge-large-zh-v1.5
```

### 6. 启动系统

#### 方式一：启动完整系统（推荐）
```bash
# 同时启动API服务和Streamlit前端
python start_full_system.py
```

#### 方式二：分别启动服务
```bash
# 启动API服务
python start_server.py

# 启动Streamlit前端（新终端窗口）
python start_streamlit.py
```

## 🌐 使用指南

### 界面访问
启动系统后，访问以下地址：
- **Streamlit前端**: http://localhost:8501 （主要用户界面）
- **API服务**: http://localhost:8000
- **API文档**: http://localhost:8000/docs
- **健康检查**: http://localhost:8000/health

### 功能模块

#### 1. Streamlit前端界面
**主要特性：**
- 🎨 现代化的用户界面设计
- 📱 响应式布局，支持多设备访问
- 🔄 实时数据更新和状态监控
- 📊 丰富的数据可视化图表
- 💾 查询历史管理和结果导出

#### 2. Text2SQL智能查询
**功能特点：**
- 🔍 自然语言理解和SQL生成
- 📋 多格式结果输出（表格、JSON、CSV）
- 💡 智能查询建议和解释
- 📝 查询历史记录和管理
- ⚡ 实时查询执行和结果展示

**使用示例：**
```
输入: "查询平安银行的基本信息"
输出: 自动生成SQL并执行，返回结果表格
```

**示例查询**：
- "查询最近30天涨幅前10的股票"
- "查询银行业股票的平均市盈率"
- "查询平安银行的技术指标"

#### 3. RAG增强股票分析
**分析维度：**
- 📊 基本面分析：财务指标、盈利能力、成长性
- 📈 技术面分析：技术指标、趋势分析、支撑阻力
- 💰 资金面分析：资金流向、成交量分析
- 🏢 行业对比：行业内股票对比分析
- ⚠️ 风险评估：风险等级和因素评估

**可视化图表：**
- 🎯 综合分析雷达图
- 📊 技术指标图表
- 📈 K线图和成交量图
- 💹 财务指标对比图
- ⚠️ 风险评估图表

**使用方式：**
- 支持股票代码、名称或热门股票选择
- 可配置分析类型、时间范围和分析深度
- 生成详细的分析报告和投资建议

#### 4. 系统管理
在"系统管理"页面：
- 查看系统状态和运行指标
- 监控数据库连接状态
- 查看系统日志和统计信息

### API使用

#### 基础API调用
```bash
# 健康检查
curl http://localhost:8000/health

# 系统状态
curl http://localhost:8000/api/v1/system/status

# 股票基础信息
curl http://localhost:8000/api/v1/analysis/stock/000001.SZ/basic
```

#### Text2SQL API
```bash
# 自然语言查询
curl -X POST http://localhost:8000/api/v1/text2sql/query \
  -H "Content-Type: application/json" \
  -d '{"natural_query": "查询平安银行的基本信息", "execute": true}'

# 查询解释
curl -X POST "http://localhost:8000/api/v1/text2sql/explain?natural_query=查询股票基本信息"
```

#### RAG搜索API
```bash
# 语义搜索
curl -X POST http://localhost:8000/api/v1/rag/search \
  -H "Content-Type: application/json" \
  -d '{"query": "平安银行", "top_k": 5}'

# 股票上下文搜索
curl http://localhost:8000/api/v1/rag/search/stock/000001.SZ
```

#### 股票分析API
```bash
# 股票分析
curl -X POST http://localhost:8000/api/v1/analysis/analyze \
  -H "Content-Type: application/json" \
  -d '{"stock_code": "000001.SZ", "analysis_type": "comprehensive"}'
```

## 📊 数据说明

### 数据表结构
- **stock_basic**: 股票基础信息
- **stock_daily_history**: 日线价格数据
- **stock_factor**: 技术指标数据
- **stock_daily_basic**: 基本面数据
- **stock_moneyflow**: 资金流向数据

### 向量数据
系统将结构化数据转换为文本并向量化，支持：
- 基础信息向量化
- 价格数据向量化
- 技术指标向量化
- 基本面数据向量化
- 资金流向向量化

## 🔧 配置说明

### 模型配置
在 `config/settings.py` 中配置：
```python
# LLM模型配置
text_generation_model = "qwen2.5:7b"
code_generation_model = "qwen2.5-coder:7b"
reasoning_model = "deepseek-r1:7b"
embedding_model = "bge-large-zh-v1.5"
```

### 系统参数
```python
# API配置
api_host = "0.0.0.0"
api_port = 8000

# 向量数据库配置
chroma_persist_directory = "./chroma_db"
chroma_collection_name = "stock_vectors"
```

## 🐛 故障排除

### 常见问题

1. **无法连接数据库**
   - 检查MySQL服务是否运行
   - 验证 `.env` 文件中的数据库配置
   - 确保数据库用户有足够权限

2. **Ollama模型加载失败**
   - 检查Ollama服务是否启动：`ollama serve`
   - 确认模型已下载：`ollama list`
   - 检查模型名称是否正确

3. **向量搜索无结果**
   - 确认已向量化股票数据
   - 检查ChromaDB数据目录权限
   - 验证向量数据库连接

4. **API响应超时**
   - LLM推理可能需要较长时间
   - 考虑使用更快的模型
   - 增加超时时间设置

### 日志查看
```bash
# 查看应用日志
tail -f logs/app.log

# 查看API访问日志
tail -f logs/api.log
```

## 📈 性能优化

### 推荐配置
- 使用SSD存储提升数据库性能
- 配置足够的内存避免频繁交换
- 使用GPU加速模型推理（如果可用）

### 扩展建议
- 部署多个API实例实现负载均衡
- 使用Redis缓存频繁查询结果
- 配置数据库读写分离

## 🤝 贡献指南

欢迎提交Issue和Pull Request来改进系统！

### 开发环境设置
```bash
# 安装开发依赖
pip install -r requirements-dev.txt

# 运行测试
python -m pytest tests/

# 代码格式化
black .
isort .
```

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持

如有问题或建议，请：
1. 查看文档和FAQ
2. 提交GitHub Issue
3. 联系开发团队

---

**注意**: 本系统仅供学习和研究使用，不构成投资建议。投资有风险，决策需谨慎。
