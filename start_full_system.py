#!/usr/bin/env python3
"""
启动完整的股票分析RAG系统
同时启动API服务和Streamlit前端
"""
import os
import sys
import time
import signal
import subprocess
import threading
from pathlib import Path

class SystemLauncher:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.api_process = None
        self.streamlit_process = None
        self.running = True
    
    def check_dependencies(self):
        """检查依赖"""
        print("🔍 检查系统依赖...")
        
        required_packages = [
            'fastapi', 'uvicorn', 'streamlit', 'pandas', 
            'pymysql', 'chromadb', 'requests', 'plotly'
        ]
        
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ 缺少依赖包: {', '.join(missing_packages)}")
            print("请运行: pip install -r requirements.txt")
            return False
        
        print("✅ 依赖检查通过")
        return True
    
    def check_ports(self):
        """检查端口占用"""
        print("🔍 检查端口占用...")
        
        import socket
        
        ports_to_check = [8000, 8501]  # API端口和Streamlit端口
        
        for port in ports_to_check:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:
                print(f"⚠️  端口 {port} 已被占用")
                print(f"请使用以下命令释放端口: lsof -ti:{port} | xargs kill -9")
                return False
        
        print("✅ 端口检查通过")
        return True
    
    def start_api_server(self):
        """启动API服务"""
        print("🚀 启动API服务...")
        
        try:
            cmd = [
                sys.executable, "-m", "uvicorn",
                "api.main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--reload"
            ]
            
            self.api_process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待API服务启动
            time.sleep(5)
            
            if self.api_process.poll() is None:
                print("✅ API服务启动成功 - http://localhost:8000")
                return True
            else:
                print("❌ API服务启动失败")
                return False
                
        except Exception as e:
            print(f"❌ API服务启动异常: {e}")
            return False
    
    def start_streamlit_app(self):
        """启动Streamlit应用"""
        print("🚀 启动Streamlit前端...")
        
        try:
            frontend_main = self.project_root / "frontend" / "main.py"
            
            cmd = [
                sys.executable, "-m", "streamlit", "run",
                str(frontend_main),
                "--server.port", "8501",
                "--server.address", "0.0.0.0",
                "--browser.gatherUsageStats", "false"
            ]
            
            self.streamlit_process = subprocess.Popen(
                cmd,
                cwd=self.project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # 等待Streamlit应用启动
            time.sleep(8)
            
            if self.streamlit_process.poll() is None:
                print("✅ Streamlit应用启动成功 - http://localhost:8501")
                return True
            else:
                print("❌ Streamlit应用启动失败")
                return False
                
        except Exception as e:
            print(f"❌ Streamlit应用启动异常: {e}")
            return False
    
    def monitor_processes(self):
        """监控进程状态"""
        while self.running:
            time.sleep(10)
            
            # 检查API进程
            if self.api_process and self.api_process.poll() is not None:
                print("⚠️  API服务进程已退出")
                self.running = False
                break
            
            # 检查Streamlit进程
            if self.streamlit_process and self.streamlit_process.poll() is not None:
                print("⚠️  Streamlit应用进程已退出")
                self.running = False
                break
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        print(f"\n📡 收到信号 {signum}，正在关闭系统...")
        self.shutdown()
    
    def shutdown(self):
        """关闭系统"""
        print("🛑 正在关闭系统...")
        self.running = False
        
        # 关闭API服务
        if self.api_process:
            print("🛑 关闭API服务...")
            self.api_process.terminate()
            try:
                self.api_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.api_process.kill()
        
        # 关闭Streamlit应用
        if self.streamlit_process:
            print("🛑 关闭Streamlit应用...")
            self.streamlit_process.terminate()
            try:
                self.streamlit_process.wait(timeout=5)
            except subprocess.TimeoutExpired:
                self.streamlit_process.kill()
        
        print("✅ 系统已关闭")
    
    def show_system_info(self):
        """显示系统信息"""
        print("=" * 60)
        print("📈 股票分析RAG系统")
        print("=" * 60)
        print(f"📁 项目目录: {self.project_root}")
        print(f"🌐 API服务: http://localhost:8000")
        print(f"🌐 前端界面: http://localhost:8501")
        print(f"📖 API文档: http://localhost:8000/docs")
        print("=" * 60)
        print("💡 使用说明:")
        print("  - 访问 http://localhost:8501 使用前端界面")
        print("  - 访问 http://localhost:8000/docs 查看API文档")
        print("  - 按 Ctrl+C 停止系统")
        print("=" * 60)
    
    def run(self):
        """运行系统"""
        try:
            # 设置信号处理器
            signal.signal(signal.SIGINT, self.signal_handler)
            signal.signal(signal.SIGTERM, self.signal_handler)
            
            # 检查依赖和端口
            if not self.check_dependencies():
                sys.exit(1)
            
            if not self.check_ports():
                sys.exit(1)
            
            # 启动API服务
            if not self.start_api_server():
                sys.exit(1)
            
            # 启动Streamlit应用
            if not self.start_streamlit_app():
                self.shutdown()
                sys.exit(1)
            
            # 显示系统信息
            self.show_system_info()
            
            # 启动进程监控
            monitor_thread = threading.Thread(target=self.monitor_processes)
            monitor_thread.daemon = True
            monitor_thread.start()
            
            # 主循环
            while self.running:
                time.sleep(1)
            
        except KeyboardInterrupt:
            print("\n👋 收到中断信号")
        except Exception as e:
            print(f"❌ 系统运行异常: {e}")
        finally:
            self.shutdown()

def main():
    """主函数"""
    launcher = SystemLauncher()
    launcher.run()

if __name__ == "__main__":
    main()
