"""
股票分析RAG系统 - Streamlit主应用
"""
import streamlit as st
import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from utils.logger import get_logger

logger = get_logger("streamlit_main")

# 页面配置
st.set_page_config(
    page_title="股票分析RAG系统",
    page_icon="📈",
    layout="wide",
    initial_sidebar_state="expanded",
    menu_items={
        'Get Help': 'https://github.com/your-repo/llm-rag',
        'Report a bug': 'https://github.com/your-repo/llm-rag/issues',
        'About': """
        # 股票分析RAG系统
        
        基于RAG技术的智能股票分析系统，结合MySQL数据库和向量数据库，
        提供Text2SQL查询和RAG增强的股票分析功能。
        
        **主要功能：**
        - 🔍 Text2SQL智能查询
        - 📊 RAG增强股票分析
        - 📈 数据可视化展示
        - 🤖 多模型支持
        """
    }
)

# 自定义CSS样式
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
        padding: 1rem;
        background: linear-gradient(90deg, #f0f8ff, #e6f3ff);
        border-radius: 10px;
        border-left: 5px solid #1f77b4;
    }
    
    .feature-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
    }
    
    .status-indicator {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 50%;
        margin-right: 8px;
    }
    
    .status-online {
        background-color: #28a745;
    }
    
    .status-offline {
        background-color: #dc3545;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主函数"""
    # 侧边栏页面选择器
    st.sidebar.title("📈 股票分析RAG系统")

    page = st.sidebar.selectbox(
        "选择功能页面",
        ["🏠 系统概览", "🔍 Text2SQL查询", "📊 股票分析"],
        help="选择要使用的功能模块"
    )

    if page == "🔍 Text2SQL查询":
        # 导入并运行Text2SQL页面
        try:
            import sys
            from pathlib import Path
            sys.path.append(str(Path(__file__).parent))
            from pages.text2sql import main as text2sql_main
            text2sql_main()
        except Exception as e:
            st.error(f"加载Text2SQL页面失败: {e}")
            st.info("请直接访问: http://localhost:8501/text2sql")

    elif page == "📊 股票分析":
        # 导入并运行股票分析页面
        try:
            import sys
            from pathlib import Path
            sys.path.append(str(Path(__file__).parent))
            from pages.stock_analysis import main as stock_analysis_main
            stock_analysis_main()
        except Exception as e:
            st.error(f"加载股票分析页面失败: {e}")
            st.info("请直接访问: http://localhost:8501/stock_analysis")

    else:
        # 默认显示系统概览
        show_system_overview()

def show_system_overview():
    """显示系统概览"""
    # 主标题
    st.markdown("""
    <div class="main-header">
        📈 股票分析RAG系统
    </div>
    """, unsafe_allow_html=True)

    # 系统状态检查
    check_system_status()

    # 功能介绍
    show_features()

    # 快速开始
    show_quick_start()

    # 系统信息
    show_system_info()

def check_system_status():
    """检查系统状态"""
    st.subheader("🔧 系统状态")
    
    col1, col2, col3, col4 = st.columns(4)
    
    try:
        # 检查数据库连接
        from database.mysql_client import mysql_client
        mysql_status = mysql_client.test_connection()

        with col1:
            status_class = "status-online" if mysql_status else "status-offline"
            status_text = "在线" if mysql_status else "离线"
            st.markdown(f"""
            <div class="feature-card">
                <span class="status-indicator {status_class}"></span>
                <strong>MySQL数据库</strong><br>
                状态: {status_text}
            </div>
            """, unsafe_allow_html=True)
    except Exception as e:
        with col1:
            st.markdown(f"""
            <div class="feature-card">
                <span class="status-indicator status-offline"></span>
                <strong>MySQL数据库</strong><br>
                状态: 配置检查中
            </div>
            """, unsafe_allow_html=True)
    
    try:
        # 检查向量数据库
        from database.vector_client import vector_client
        vector_info = vector_client.get_collection_info()
        vector_status = True
        
        with col2:
            st.markdown(f"""
            <div class="feature-card">
                <span class="status-indicator status-online"></span>
                <strong>向量数据库</strong><br>
                状态: 在线<br>
                文档数: {vector_info.get('count', 0)}
            </div>
            """, unsafe_allow_html=True)
    except Exception as e:
        with col2:
            st.markdown(f"""
            <div class="feature-card">
                <span class="status-indicator status-offline"></span>
                <strong>向量数据库</strong><br>
                状态: 连接失败
            </div>
            """, unsafe_allow_html=True)
    
    try:
        # 检查Ollama服务
        from llm.model_manager import model_manager
        model_status = model_manager.get_model_status()
        ollama_available = model_status.get("ollama_available", False)
        available_models = model_status.get("available_models", [])
        
        with col3:
            status_class = "status-online" if ollama_available else "status-offline"
            status_text = "在线" if ollama_available else "离线"
            st.markdown(f"""
            <div class="feature-card">
                <span class="status-indicator {status_class}"></span>
                <strong>Ollama服务</strong><br>
                状态: {status_text}<br>
                模型数: {len(available_models)}
            </div>
            """, unsafe_allow_html=True)
    except Exception as e:
        with col3:
            st.markdown(f"""
            <div class="feature-card">
                <span class="status-indicator status-offline"></span>
                <strong>Ollama服务</strong><br>
                状态: 连接失败
            </div>
            """, unsafe_allow_html=True)
    
    # API服务状态
    with col4:
        st.markdown(f"""
        <div class="feature-card">
            <span class="status-indicator status-online"></span>
            <strong>API服务</strong><br>
            状态: 运行中<br>
            端口: {settings.api_port}
        </div>
        """, unsafe_allow_html=True)

def show_features():
    """显示功能特性"""
    st.subheader("🚀 主要功能")
    
    col1, col2 = st.columns(2)
    
    with col1:
        st.markdown("""
        <div class="feature-card">
            <h4>🔍 Text2SQL智能查询</h4>
            <p>使用自然语言查询股票数据，系统自动生成SQL并执行查询。</p>
            <ul>
                <li>自然语言理解</li>
                <li>智能SQL生成</li>
                <li>多格式结果输出</li>
                <li>查询历史管理</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)
    
    with col2:
        st.markdown("""
        <div class="feature-card">
            <h4>📊 RAG增强股票分析</h4>
            <p>结合历史数据和实时信息，提供深度股票分析报告。</p>
            <ul>
                <li>多维度数据融合</li>
                <li>智能分析报告</li>
                <li>可视化图表</li>
                <li>投资建议生成</li>
            </ul>
        </div>
        """, unsafe_allow_html=True)

def show_quick_start():
    """显示快速开始指南"""
    st.subheader("⚡ 快速开始")

    st.markdown("""
    ### 📋 使用指南

    **方式一：直接访问页面**
    - 🔍 **Text2SQL查询**: 在浏览器新标签页中打开 `http://localhost:8501/text2sql`
    - 📊 **股票分析**: 在浏览器新标签页中打开 `http://localhost:8501/stock_analysis`

    **方式二：使用侧边栏导航**
    - 点击左侧边栏的页面链接进行导航

    **方式三：API接口**
    - 📖 **API文档**: http://localhost:8000/docs
    - 🔗 **健康检查**: http://localhost:8000/health
    """)

    col1, col2, col3 = st.columns(3)

    with col1:
        st.info("🔍 **Text2SQL查询**\n\n使用自然语言查询股票数据")

    with col2:
        st.info("📊 **股票分析**\n\n基于RAG的智能股票分析")

    with col3:
        st.info("📖 **API文档**\n\n查看完整的API接口文档")

    # 添加快捷链接
    st.markdown("---")
    st.markdown("### 🔗 快捷链接")

    link_col1, link_col2, link_col3, link_col4 = st.columns(4)

    with link_col1:
        st.markdown("[🔍 Text2SQL查询](http://localhost:8501/text2sql)")

    with link_col2:
        st.markdown("[📊 股票分析](http://localhost:8501/stock_analysis)")

    with link_col3:
        st.markdown(f"[📖 API文档](http://localhost:{settings.api_port}/docs)")

    with link_col4:
        st.markdown(f"[❤️ 健康检查](http://localhost:{settings.api_port}/health)")

def show_system_info():
    """显示系统信息"""
    with st.expander("📋 系统信息", expanded=False):
        col1, col2 = st.columns(2)
        
        with col1:
            st.markdown("**环境配置**")
            st.write(f"- 环境: {settings.environment}")
            st.write(f"- 调试模式: {settings.debug}")
            st.write(f"- API端口: {settings.api_port}")
            
        with col2:
            st.markdown("**数据库配置**")
            st.write(f"- MySQL: {settings.mysql_host}:{settings.mysql_port}")
            st.write(f"- 数据库: {settings.mysql_database}")
            st.write(f"- 向量数据库: ChromaDB")

if __name__ == "__main__":
    main()
