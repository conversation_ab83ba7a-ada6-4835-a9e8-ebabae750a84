"""
Text2SQL智能查询页面
"""
import streamlit as st
import pandas as pd
import json
import sys
import os
from pathlib import Path
import requests
from datetime import datetime

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from utils.logger import get_logger

logger = get_logger("streamlit_text2sql")

# 页面配置
st.set_page_config(
    page_title="Text2SQL查询 - 股票分析RAG系统",
    page_icon="🔍",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
    .query-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #1f77b4;
        margin-bottom: 1rem;
    }
    
    .result-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #dee2e6;
        margin: 1rem 0;
    }
    
    .sql-code {
        background: #2d3748;
        color: #e2e8f0;
        padding: 1rem;
        border-radius: 8px;
        font-family: 'Courier New', monospace;
        margin: 0.5rem 0;
    }
    
    .suggestion-chip {
        display: inline-block;
        background: #e3f2fd;
        color: #1976d2;
        padding: 0.3rem 0.8rem;
        border-radius: 20px;
        margin: 0.2rem;
        cursor: pointer;
        border: 1px solid #bbdefb;
    }
    
    .suggestion-chip:hover {
        background: #bbdefb;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主函数"""
    st.title("🔍 Text2SQL智能查询")
    st.markdown("使用自然语言查询股票数据，系统自动生成SQL并执行")
    
    # 侧边栏配置
    setup_sidebar()
    
    # 主查询界面
    query_interface()
    
    # 查询历史
    show_query_history()

def setup_sidebar():
    """设置侧边栏"""
    st.sidebar.header("⚙️ 查询配置")
    
    # 输出格式选择
    st.session_state.format_type = st.sidebar.selectbox(
        "输出格式",
        ["table", "json", "csv"],
        index=0,
        help="选择查询结果的输出格式"
    )
    
    # 是否执行查询
    st.session_state.execute_query = st.sidebar.checkbox(
        "执行查询",
        value=True,
        help="是否实际执行生成的SQL查询"
    )
    
    # 结果限制
    st.session_state.limit_results = st.sidebar.number_input(
        "结果限制",
        min_value=1,
        max_value=1000,
        value=100,
        help="限制返回的结果数量"
    )
    
    # 查询建议
    st.sidebar.header("💡 查询建议")
    show_query_suggestions()

def show_query_suggestions():
    """显示查询建议"""
    suggestions = [
        "查询平安银行的基本信息",
        "显示市值最大的10只股票",
        "查找今日涨幅超过5%的股票",
        "显示银行板块的股票列表",
        "查询贵州茅台的历史价格",
        "找出PE比率最低的股票",
        "显示最近一周的交易量排行",
        "查询科技股的平均市盈率"
    ]
    
    for suggestion in suggestions:
        if st.sidebar.button(suggestion, key=f"suggestion_{suggestion}"):
            st.session_state.natural_query = suggestion
            st.rerun()

def query_interface():
    """查询界面"""
    # 查询输入
    col1, col2 = st.columns([4, 1])
    
    with col1:
        natural_query = st.text_area(
            "输入您的查询",
            value=st.session_state.get('natural_query', ''),
            height=100,
            placeholder="例如：查询平安银行的基本信息",
            help="使用自然语言描述您想要查询的内容"
        )
    
    with col2:
        st.markdown("<br>", unsafe_allow_html=True)
        if st.button("🔍 查询", type="primary", use_container_width=True):
            if natural_query.strip():
                execute_query(natural_query)
            else:
                st.error("请输入查询内容")
        
        if st.button("💡 解释查询", use_container_width=True):
            if natural_query.strip():
                explain_query(natural_query)
            else:
                st.error("请输入查询内容")
    
    # 显示查询结果
    if 'query_result' in st.session_state:
        show_query_result(st.session_state.query_result)

def execute_query(natural_query: str):
    """执行查询"""
    with st.spinner("正在处理查询..."):
        try:
            # 调用API
            api_url = f"http://localhost:{settings.api_port}/api/v1/text2sql/query"
            
            payload = {
                "natural_query": natural_query,
                "format_type": st.session_state.format_type,
                "execute": st.session_state.execute_query,
                "limit": st.session_state.limit_results,
                "include_suggestions": True
            }
            
            response = requests.post(api_url, json=payload, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    st.session_state.query_result = result["data"]
                    st.success("查询执行成功！")
                    
                    # 保存到历史记录
                    save_to_history(natural_query, result["data"])
                else:
                    st.error(f"查询失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"API请求失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            st.error(f"网络请求失败: {str(e)}")
        except Exception as e:
            st.error(f"查询执行失败: {str(e)}")
            logger.error(f"查询执行失败: {e}")

def explain_query(natural_query: str):
    """解释查询"""
    with st.spinner("正在解释查询..."):
        try:
            api_url = f"http://localhost:{settings.api_port}/api/v1/text2sql/explain"
            
            params = {"natural_query": natural_query}
            response = requests.post(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    show_explanation(result["data"])
                else:
                    st.error(f"查询解释失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            st.error(f"查询解释失败: {str(e)}")
            logger.error(f"查询解释失败: {e}")

def show_explanation(explanation_data):
    """显示查询解释"""
    st.subheader("💡 查询解释")
    
    with st.expander("查看详细解释", expanded=True):
        # 查询意图
        if "intent" in explanation_data:
            st.markdown("**查询意图:**")
            st.write(explanation_data["intent"])
        
        # 生成的SQL
        if "sql" in explanation_data:
            st.markdown("**生成的SQL:**")
            st.code(explanation_data["sql"], language="sql")
        
        # 涉及的表和字段
        if "tables" in explanation_data:
            st.markdown("**涉及的表:**")
            for table in explanation_data["tables"]:
                st.write(f"- {table}")
        
        if "columns" in explanation_data:
            st.markdown("**涉及的字段:**")
            for column in explanation_data["columns"]:
                st.write(f"- {column}")
        
        # 查询建议
        if "suggestions" in explanation_data:
            st.markdown("**相关查询建议:**")
            for suggestion in explanation_data["suggestions"]:
                st.write(f"- {suggestion}")

def show_query_result(result_data):
    """显示查询结果"""
    st.subheader("📊 查询结果")
    
    # 显示生成的SQL
    if "sql" in result_data:
        st.markdown("**生成的SQL:**")
        st.code(result_data["sql"], language="sql")
    
    # 显示执行结果
    if "data" in result_data and result_data["data"]:
        st.markdown("**查询结果:**")
        
        # 根据格式类型显示结果
        if st.session_state.format_type == "table":
            if isinstance(result_data["data"], list) and result_data["data"]:
                df = pd.DataFrame(result_data["data"])
                st.dataframe(df, use_container_width=True)
                
                # 提供下载选项
                csv = df.to_csv(index=False)
                st.download_button(
                    label="📥 下载CSV",
                    data=csv,
                    file_name=f"query_result_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
                    mime="text/csv"
                )
            else:
                st.info("查询未返回数据")
                
        elif st.session_state.format_type == "json":
            st.json(result_data["data"])
            
        elif st.session_state.format_type == "csv":
            if isinstance(result_data["data"], list) and result_data["data"]:
                df = pd.DataFrame(result_data["data"])
                st.text(df.to_csv(index=False))
    
    # 显示执行信息
    if "execution_info" in result_data:
        with st.expander("执行信息"):
            info = result_data["execution_info"]
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("执行时间", f"{info.get('execution_time', 0):.3f}s")
            with col2:
                st.metric("返回行数", info.get('row_count', 0))
            with col3:
                st.metric("影响行数", info.get('affected_rows', 0))

def save_to_history(query: str, result: dict):
    """保存查询历史"""
    if 'query_history' not in st.session_state:
        st.session_state.query_history = []
    
    history_item = {
        "timestamp": datetime.now().isoformat(),
        "query": query,
        "sql": result.get("sql", ""),
        "success": True,
        "row_count": result.get("execution_info", {}).get("row_count", 0)
    }
    
    st.session_state.query_history.insert(0, history_item)
    
    # 限制历史记录数量
    if len(st.session_state.query_history) > 50:
        st.session_state.query_history = st.session_state.query_history[:50]

def show_query_history():
    """显示查询历史"""
    if 'query_history' in st.session_state and st.session_state.query_history:
        with st.expander("📝 查询历史", expanded=False):
            for i, item in enumerate(st.session_state.query_history[:10]):  # 只显示最近10条
                col1, col2, col3 = st.columns([3, 1, 1])
                
                with col1:
                    st.write(f"**{item['query']}**")
                    st.code(item['sql'], language="sql")
                
                with col2:
                    st.write(f"时间: {item['timestamp'][:19]}")
                    st.write(f"行数: {item['row_count']}")
                
                with col3:
                    if st.button("重新执行", key=f"rerun_{i}"):
                        st.session_state.natural_query = item['query']
                        st.rerun()
                
                st.divider()

if __name__ == "__main__":
    main()
