"""
股票分析页面 - RAG增强的股票分析
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import sys
import os
from pathlib import Path
import requests
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from utils.logger import get_logger

logger = get_logger("streamlit_stock_analysis")

# 页面配置
st.set_page_config(
    page_title="股票分析 - 股票分析RAG系统",
    page_icon="📊",
    layout="wide"
)

# 自定义CSS
st.markdown("""
<style>
    .analysis-card {
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-left: 4px solid #28a745;
        margin-bottom: 1rem;
    }
    
    .metric-card {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 1rem;
        border-radius: 10px;
        text-align: center;
        margin: 0.5rem 0;
    }
    
    .recommendation-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #ffc107;
        margin: 1rem 0;
    }
    
    .risk-high {
        border-left-color: #dc3545;
    }
    
    .risk-medium {
        border-left-color: #ffc107;
    }
    
    .risk-low {
        border-left-color: #28a745;
    }
</style>
""", unsafe_allow_html=True)

def main():
    """主函数"""
    st.title("📊 股票分析")
    st.markdown("基于RAG技术的智能股票分析，结合历史数据和实时信息")
    
    # 侧边栏配置
    setup_sidebar()
    
    # 主分析界面
    analysis_interface()

def setup_sidebar():
    """设置侧边栏"""
    st.sidebar.header("📈 分析配置")
    
    # 股票选择方式
    selection_method = st.sidebar.radio(
        "选择方式",
        ["股票代码", "股票名称", "热门股票"],
        help="选择股票的方式"
    )
    
    if selection_method == "股票代码":
        st.session_state.stock_input = st.sidebar.text_input(
            "股票代码",
            value="000001",
            help="输入6位股票代码，如：000001"
        )
    elif selection_method == "股票名称":
        st.session_state.stock_input = st.sidebar.text_input(
            "股票名称",
            value="平安银行",
            help="输入股票名称，如：平安银行"
        )
    else:
        # 热门股票列表
        popular_stocks = [
            "000001 - 平安银行",
            "000002 - 万科A",
            "600036 - 招商银行",
            "600519 - 贵州茅台",
            "000858 - 五粮液",
            "600000 - 浦发银行"
        ]
        selected_stock = st.sidebar.selectbox(
            "热门股票",
            popular_stocks,
            help="选择热门股票进行分析"
        )
        st.session_state.stock_input = selected_stock.split(" - ")[0]
    
    # 分析类型
    st.session_state.analysis_types = st.sidebar.multiselect(
        "分析类型",
        ["基本面分析", "技术面分析", "资金面分析", "行业对比", "风险评估"],
        default=["基本面分析", "技术面分析"],
        help="选择要进行的分析类型"
    )
    
    # 时间范围
    st.session_state.time_range = st.sidebar.selectbox(
        "分析时间范围",
        ["1个月", "3个月", "6个月", "1年", "2年"],
        index=2,
        help="选择分析的时间范围"
    )
    
    # 分析深度
    st.session_state.analysis_depth = st.sidebar.slider(
        "分析深度",
        min_value=1,
        max_value=5,
        value=3,
        help="1-简单分析，5-深度分析"
    )

def analysis_interface():
    """分析界面"""
    # 分析按钮
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        if st.button("🔍 开始分析", type="primary", use_container_width=True):
            if st.session_state.get('stock_input'):
                perform_analysis()
            else:
                st.error("请输入股票代码或名称")
    
    with col2:
        if st.button("📊 快速概览", use_container_width=True):
            if st.session_state.get('stock_input'):
                show_quick_overview()
            else:
                st.error("请输入股票代码或名称")
    
    with col3:
        if st.button("🔄 刷新数据", use_container_width=True):
            st.cache_data.clear()
            st.success("缓存已清除")
    
    # 显示分析结果
    if 'analysis_result' in st.session_state:
        show_analysis_result(st.session_state.analysis_result)

def perform_analysis():
    """执行股票分析"""
    with st.spinner("正在进行股票分析..."):
        try:
            # 调用分析API
            api_url = f"http://localhost:{settings.api_port}/api/v1/analysis/stock"
            
            payload = {
                "stock_input": st.session_state.stock_input,
                "analysis_types": st.session_state.analysis_types,
                "time_range": st.session_state.time_range,
                "analysis_depth": st.session_state.analysis_depth
            }
            
            response = requests.post(api_url, json=payload, timeout=60)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    st.session_state.analysis_result = result["data"]
                    st.success("分析完成！")
                else:
                    st.error(f"分析失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"API请求失败: {response.status_code}")
                
        except requests.exceptions.RequestException as e:
            st.error(f"网络请求失败: {str(e)}")
        except Exception as e:
            st.error(f"分析执行失败: {str(e)}")
            logger.error(f"分析执行失败: {e}")

def show_quick_overview():
    """显示快速概览"""
    with st.spinner("正在获取股票概览..."):
        try:
            # 调用概览API
            api_url = f"http://localhost:{settings.api_port}/api/v1/analysis/overview"
            
            params = {"stock_input": st.session_state.stock_input}
            response = requests.get(api_url, params=params, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    show_stock_overview(result["data"])
                else:
                    st.error(f"获取概览失败: {result.get('message', '未知错误')}")
            else:
                st.error(f"API请求失败: {response.status_code}")
                
        except Exception as e:
            st.error(f"获取概览失败: {str(e)}")
            logger.error(f"获取概览失败: {e}")

def show_stock_overview(overview_data):
    """显示股票概览"""
    st.subheader("📈 股票概览")
    
    # 基本信息
    if "basic_info" in overview_data:
        info = overview_data["basic_info"]
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric(
                "股票名称",
                info.get("name", "N/A"),
                help="股票名称"
            )
        
        with col2:
            st.metric(
                "股票代码",
                info.get("code", "N/A"),
                help="股票代码"
            )
        
        with col3:
            current_price = info.get("current_price", 0)
            price_change = info.get("price_change", 0)
            st.metric(
                "当前价格",
                f"¥{current_price:.2f}",
                f"{price_change:+.2f}",
                help="当前股价及涨跌"
            )
        
        with col4:
            change_percent = info.get("change_percent", 0)
            st.metric(
                "涨跌幅",
                f"{change_percent:.2f}%",
                help="涨跌幅百分比"
            )
    
    # 关键指标
    if "key_metrics" in overview_data:
        st.subheader("📊 关键指标")
        metrics = overview_data["key_metrics"]
        
        col1, col2, col3, col4 = st.columns(4)
        
        with col1:
            st.metric("市值", f"{metrics.get('market_cap', 0):.2f}亿")
        with col2:
            st.metric("市盈率", f"{metrics.get('pe_ratio', 0):.2f}")
        with col3:
            st.metric("市净率", f"{metrics.get('pb_ratio', 0):.2f}")
        with col4:
            st.metric("成交量", f"{metrics.get('volume', 0):.0f}万手")

def show_analysis_result(result_data):
    """显示分析结果"""
    st.subheader("📊 分析结果")
    
    # 创建标签页
    tabs = st.tabs(["📈 综合分析", "📊 技术分析", "💰 基本面分析", "⚠️ 风险评估", "📋 投资建议"])
    
    with tabs[0]:
        show_comprehensive_analysis(result_data)
    
    with tabs[1]:
        show_technical_analysis(result_data)
    
    with tabs[2]:
        show_fundamental_analysis(result_data)
    
    with tabs[3]:
        show_risk_assessment(result_data)
    
    with tabs[4]:
        show_investment_recommendation(result_data)

def show_comprehensive_analysis(result_data):
    """显示综合分析"""
    if "comprehensive_analysis" in result_data:
        analysis = result_data["comprehensive_analysis"]
        
        # 分析摘要
        if "summary" in analysis:
            st.markdown("### 📝 分析摘要")
            st.write(analysis["summary"])
        
        # 关键发现
        if "key_findings" in analysis:
            st.markdown("### 🔍 关键发现")
            for finding in analysis["key_findings"]:
                st.write(f"• {finding}")
        
        # 评分
        if "scores" in analysis:
            st.markdown("### 📊 综合评分")
            scores = analysis["scores"]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("投资价值", f"{scores.get('investment_value', 0)}/10")
            with col2:
                st.metric("风险等级", f"{scores.get('risk_level', 0)}/10")
            with col3:
                st.metric("推荐指数", f"{scores.get('recommendation', 0)}/10")

def show_technical_analysis(result_data):
    """显示技术分析"""
    if "technical_analysis" in result_data:
        analysis = result_data["technical_analysis"]
        
        # 技术指标
        if "indicators" in analysis:
            st.markdown("### 📈 技术指标")
            indicators = analysis["indicators"]
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("RSI", f"{indicators.get('rsi', 0):.2f}")
            with col2:
                st.metric("MACD", f"{indicators.get('macd', 0):.4f}")
            with col3:
                st.metric("KDJ-K", f"{indicators.get('kdj_k', 0):.2f}")
            with col4:
                st.metric("布林带位置", f"{indicators.get('bollinger_position', 0):.2f}")
        
        # 趋势分析
        if "trend_analysis" in analysis:
            st.markdown("### 📊 趋势分析")
            st.write(analysis["trend_analysis"])
        
        # 支撑阻力位
        if "support_resistance" in analysis:
            st.markdown("### 🎯 支撑阻力位")
            sr = analysis["support_resistance"]
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.write("**支撑位:**")
                for level in sr.get("support_levels", []):
                    st.write(f"• ¥{level:.2f}")
            
            with col2:
                st.write("**阻力位:**")
                for level in sr.get("resistance_levels", []):
                    st.write(f"• ¥{level:.2f}")

def show_fundamental_analysis(result_data):
    """显示基本面分析"""
    if "fundamental_analysis" in result_data:
        analysis = result_data["fundamental_analysis"]
        
        # 财务指标
        if "financial_metrics" in analysis:
            st.markdown("### 💰 财务指标")
            metrics = analysis["financial_metrics"]
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                st.metric("净资产收益率", f"{metrics.get('roe', 0):.2f}%")
            with col2:
                st.metric("资产负债率", f"{metrics.get('debt_ratio', 0):.2f}%")
            with col3:
                st.metric("毛利率", f"{metrics.get('gross_margin', 0):.2f}%")
            with col4:
                st.metric("净利率", f"{metrics.get('net_margin', 0):.2f}%")
        
        # 盈利能力
        if "profitability" in analysis:
            st.markdown("### 📈 盈利能力分析")
            st.write(analysis["profitability"])
        
        # 成长性分析
        if "growth_analysis" in analysis:
            st.markdown("### 🚀 成长性分析")
            st.write(analysis["growth_analysis"])

def show_risk_assessment(result_data):
    """显示风险评估"""
    if "risk_assessment" in result_data:
        risk = result_data["risk_assessment"]
        
        # 风险等级
        if "risk_level" in risk:
            risk_level = risk["risk_level"]
            risk_class = f"risk-{risk_level.lower()}"
            
            st.markdown(f"""
            <div class="recommendation-card {risk_class}">
                <h4>⚠️ 风险等级: {risk_level.upper()}</h4>
            </div>
            """, unsafe_allow_html=True)
        
        # 风险因素
        if "risk_factors" in risk:
            st.markdown("### ⚠️ 主要风险因素")
            for factor in risk["risk_factors"]:
                st.write(f"• {factor}")
        
        # 风险建议
        if "risk_suggestions" in risk:
            st.markdown("### 💡 风险控制建议")
            for suggestion in risk["risk_suggestions"]:
                st.write(f"• {suggestion}")

def show_investment_recommendation(result_data):
    """显示投资建议"""
    if "investment_recommendation" in result_data:
        recommendation = result_data["investment_recommendation"]
        
        # 投资建议
        if "recommendation" in recommendation:
            rec = recommendation["recommendation"]
            
            st.markdown(f"""
            <div class="recommendation-card">
                <h4>💡 投资建议: {rec}</h4>
            </div>
            """, unsafe_allow_html=True)
        
        # 目标价位
        if "target_price" in recommendation:
            target = recommendation["target_price"]
            
            col1, col2, col3 = st.columns(3)
            
            with col1:
                st.metric("目标价", f"¥{target.get('target', 0):.2f}")
            with col2:
                st.metric("止损价", f"¥{target.get('stop_loss', 0):.2f}")
            with col3:
                st.metric("止盈价", f"¥{target.get('take_profit', 0):.2f}")
        
        # 投资理由
        if "reasons" in recommendation:
            st.markdown("### 📝 投资理由")
            for reason in recommendation["reasons"]:
                st.write(f"• {reason}")
        
        # 注意事项
        if "notes" in recommendation:
            st.markdown("### ⚠️ 注意事项")
            for note in recommendation["notes"]:
                st.write(f"• {note}")

if __name__ == "__main__":
    main()
