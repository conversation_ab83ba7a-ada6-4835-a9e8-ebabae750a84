"""
通用UI组件
"""
import streamlit as st
import pandas as pd
from typing import Dict, List, Any, Optional
from datetime import datetime

def show_loading_spinner(message: str = "正在处理..."):
    """显示加载动画"""
    return st.spinner(message)

def show_success_message(message: str):
    """显示成功消息"""
    st.success(message)

def show_error_message(message: str):
    """显示错误消息"""
    st.error(message)

def show_warning_message(message: str):
    """显示警告消息"""
    st.warning(message)

def show_info_message(message: str):
    """显示信息消息"""
    st.info(message)

def create_metric_card(title: str, value: str, delta: Optional[str] = None, help_text: Optional[str] = None):
    """创建指标卡片"""
    return st.metric(
        label=title,
        value=value,
        delta=delta,
        help=help_text
    )

def create_status_indicator(status: bool, online_text: str = "在线", offline_text: str = "离线"):
    """创建状态指示器"""
    if status:
        st.markdown(f"""
        <div style="display: flex; align-items: center;">
            <div style="width: 12px; height: 12px; background-color: #28a745; border-radius: 50%; margin-right: 8px;"></div>
            <span>{online_text}</span>
        </div>
        """, unsafe_allow_html=True)
    else:
        st.markdown(f"""
        <div style="display: flex; align-items: center;">
            <div style="width: 12px; height: 12px; background-color: #dc3545; border-radius: 50%; margin-right: 8px;"></div>
            <span>{offline_text}</span>
        </div>
        """, unsafe_allow_html=True)

def create_info_card(title: str, content: str, card_type: str = "default"):
    """创建信息卡片"""
    card_styles = {
        "default": "border-left: 4px solid #1f77b4;",
        "success": "border-left: 4px solid #28a745;",
        "warning": "border-left: 4px solid #ffc107;",
        "error": "border-left: 4px solid #dc3545;",
        "info": "border-left: 4px solid #17a2b8;"
    }
    
    style = card_styles.get(card_type, card_styles["default"])
    
    st.markdown(f"""
    <div style="
        background: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        {style}
        margin-bottom: 1rem;
    ">
        <h4>{title}</h4>
        <p>{content}</p>
    </div>
    """, unsafe_allow_html=True)

def create_progress_bar(progress: float, text: str = ""):
    """创建进度条"""
    st.progress(progress, text=text)

def create_data_table(data: List[Dict], title: str = "数据表格"):
    """创建数据表格"""
    if data:
        st.subheader(title)
        df = pd.DataFrame(data)
        st.dataframe(df, use_container_width=True)
        
        # 提供下载选项
        csv = df.to_csv(index=False)
        st.download_button(
            label="📥 下载CSV",
            data=csv,
            file_name=f"{title}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv",
            mime="text/csv"
        )
    else:
        st.info("暂无数据")

def create_expandable_section(title: str, content: Any, expanded: bool = False):
    """创建可展开的部分"""
    with st.expander(title, expanded=expanded):
        if isinstance(content, str):
            st.write(content)
        elif isinstance(content, dict):
            st.json(content)
        elif isinstance(content, list):
            for item in content:
                st.write(f"• {item}")
        else:
            st.write(content)

def create_tabs(tab_names: List[str], tab_contents: List[Any]):
    """创建标签页"""
    tabs = st.tabs(tab_names)
    
    for i, (tab, content) in enumerate(zip(tabs, tab_contents)):
        with tab:
            if callable(content):
                content()
            else:
                st.write(content)

def create_sidebar_section(title: str, content_func):
    """创建侧边栏部分"""
    st.sidebar.header(title)
    content_func()

def create_columns_layout(column_ratios: List[int], contents: List[Any]):
    """创建列布局"""
    cols = st.columns(column_ratios)
    
    for col, content in zip(cols, contents):
        with col:
            if callable(content):
                content()
            else:
                st.write(content)

def create_button_group(buttons: List[Dict[str, Any]], layout: str = "horizontal"):
    """创建按钮组"""
    if layout == "horizontal":
        cols = st.columns(len(buttons))
        for i, (col, button) in enumerate(zip(cols, buttons)):
            with col:
                if st.button(
                    button.get("label", f"按钮{i+1}"),
                    key=button.get("key", f"btn_{i}"),
                    type=button.get("type", "secondary"),
                    use_container_width=button.get("full_width", True)
                ):
                    if "callback" in button and callable(button["callback"]):
                        button["callback"]()
    else:  # vertical
        for i, button in enumerate(buttons):
            if st.button(
                button.get("label", f"按钮{i+1}"),
                key=button.get("key", f"btn_{i}"),
                type=button.get("type", "secondary"),
                use_container_width=button.get("full_width", True)
            ):
                if "callback" in button and callable(button["callback"]):
                    button["callback"]()

def create_form(form_key: str, fields: List[Dict[str, Any]], submit_label: str = "提交"):
    """创建表单"""
    with st.form(form_key):
        form_data = {}
        
        for field in fields:
            field_type = field.get("type", "text")
            field_key = field.get("key", "")
            field_label = field.get("label", "")
            field_value = field.get("default", "")
            field_help = field.get("help", "")
            
            if field_type == "text":
                form_data[field_key] = st.text_input(field_label, value=field_value, help=field_help)
            elif field_type == "textarea":
                form_data[field_key] = st.text_area(field_label, value=field_value, help=field_help)
            elif field_type == "number":
                form_data[field_key] = st.number_input(
                    field_label,
                    value=field_value,
                    min_value=field.get("min_value"),
                    max_value=field.get("max_value"),
                    help=field_help
                )
            elif field_type == "selectbox":
                form_data[field_key] = st.selectbox(
                    field_label,
                    options=field.get("options", []),
                    index=field.get("index", 0),
                    help=field_help
                )
            elif field_type == "multiselect":
                form_data[field_key] = st.multiselect(
                    field_label,
                    options=field.get("options", []),
                    default=field.get("default", []),
                    help=field_help
                )
            elif field_type == "checkbox":
                form_data[field_key] = st.checkbox(field_label, value=field_value, help=field_help)
            elif field_type == "slider":
                form_data[field_key] = st.slider(
                    field_label,
                    min_value=field.get("min_value", 0),
                    max_value=field.get("max_value", 100),
                    value=field_value,
                    help=field_help
                )
        
        submitted = st.form_submit_button(submit_label)
        
        if submitted:
            return form_data
    
    return None

def create_file_uploader(label: str, file_types: List[str] = None, multiple: bool = False):
    """创建文件上传器"""
    return st.file_uploader(
        label,
        type=file_types,
        accept_multiple_files=multiple
    )

def create_download_button(data: Any, filename: str, label: str = "下载", mime_type: str = "text/plain"):
    """创建下载按钮"""
    return st.download_button(
        label=label,
        data=data,
        file_name=filename,
        mime=mime_type
    )

def create_code_block(code: str, language: str = "python"):
    """创建代码块"""
    st.code(code, language=language)

def create_json_display(data: Dict):
    """创建JSON显示"""
    st.json(data)

def create_markdown_content(content: str):
    """创建Markdown内容"""
    st.markdown(content, unsafe_allow_html=True)

def create_alert_box(message: str, alert_type: str = "info"):
    """创建警告框"""
    alert_styles = {
        "info": {"color": "#0c5460", "background": "#d1ecf1", "border": "#bee5eb"},
        "success": {"color": "#155724", "background": "#d4edda", "border": "#c3e6cb"},
        "warning": {"color": "#856404", "background": "#fff3cd", "border": "#ffeaa7"},
        "error": {"color": "#721c24", "background": "#f8d7da", "border": "#f5c6cb"}
    }
    
    style = alert_styles.get(alert_type, alert_styles["info"])
    
    st.markdown(f"""
    <div style="
        color: {style['color']};
        background-color: {style['background']};
        border: 1px solid {style['border']};
        padding: 0.75rem 1.25rem;
        margin-bottom: 1rem;
        border-radius: 0.25rem;
    ">
        {message}
    </div>
    """, unsafe_allow_html=True)
