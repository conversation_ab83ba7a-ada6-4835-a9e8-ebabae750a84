"""
分析结果可视化
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional
import numpy as np

def create_analysis_summary_chart(analysis_data: Dict[str, Any]) -> go.Figure:
    """创建分析摘要图表"""
    categories = ['投资价值', '风险等级', '技术面', '基本面', '资金面']
    scores = [
        analysis_data.get('investment_value', 5),
        10 - analysis_data.get('risk_level', 5),  # 风险等级反向
        analysis_data.get('technical_score', 5),
        analysis_data.get('fundamental_score', 5),
        analysis_data.get('capital_score', 5)
    ]
    
    fig = go.Figure(data=go.Scatterpolar(
        r=scores,
        theta=categories,
        fill='toself',
        name='综合评分'
    ))
    
    fig.update_layout(
        polar=dict(
            radialaxis=dict(
                visible=True,
                range=[0, 10]
            )),
        showlegend=True,
        title="综合分析雷达图",
        height=500
    )
    
    return fig

def create_financial_metrics_chart(financial_data: Dict[str, float]) -> go.Figure:
    """创建财务指标图表"""
    metrics = list(financial_data.keys())
    values = list(financial_data.values())
    
    fig = go.Figure(data=[
        go.Bar(
            x=metrics,
            y=values,
            marker_color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd'][:len(metrics)]
        )
    ])
    
    fig.update_layout(
        title="财务指标对比",
        xaxis_title="指标",
        yaxis_title="数值",
        height=400
    )
    
    return fig

def create_risk_assessment_chart(risk_data: Dict[str, Any]) -> go.Figure:
    """创建风险评估图表"""
    risk_factors = risk_data.get('risk_factors', {})
    
    if not risk_factors:
        # 默认风险因素
        risk_factors = {
            '市场风险': 3,
            '行业风险': 2,
            '公司风险': 4,
            '流动性风险': 2,
            '信用风险': 1
        }
    
    factors = list(risk_factors.keys())
    levels = list(risk_factors.values())
    
    colors = ['green' if level <= 2 else 'yellow' if level <= 3 else 'red' for level in levels]
    
    fig = go.Figure(data=[
        go.Bar(
            x=factors,
            y=levels,
            marker_color=colors,
            text=levels,
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title="风险因素评估",
        xaxis_title="风险因素",
        yaxis_title="风险等级 (1-5)",
        height=400,
        yaxis=dict(range=[0, 5])
    )
    
    return fig

def create_performance_comparison_chart(performance_data: Dict[str, List[float]]) -> go.Figure:
    """创建业绩对比图表"""
    fig = go.Figure()
    
    periods = ['1个月', '3个月', '6个月', '1年', '2年']
    
    for stock_name, returns in performance_data.items():
        fig.add_trace(go.Scatter(
            x=periods,
            y=returns,
            mode='lines+markers',
            name=stock_name,
            line=dict(width=3)
        ))
    
    fig.update_layout(
        title="业绩对比",
        xaxis_title="时间周期",
        yaxis_title="收益率 (%)",
        height=400,
        hovermode='x unified'
    )
    
    return fig

def create_sector_analysis_chart(sector_data: Dict[str, Any]) -> go.Figure:
    """创建行业分析图表"""
    fig = make_subplots(
        rows=1, cols=2,
        subplot_titles=('行业市值分布', '行业涨跌幅'),
        specs=[[{"type": "pie"}, {"type": "bar"}]]
    )
    
    # 行业市值分布饼图
    if 'market_cap_distribution' in sector_data:
        distribution = sector_data['market_cap_distribution']
        fig.add_trace(go.Pie(
            labels=list(distribution.keys()),
            values=list(distribution.values()),
            name="市值分布"
        ), row=1, col=1)
    
    # 行业涨跌幅柱状图
    if 'sector_performance' in sector_data:
        performance = sector_data['sector_performance']
        sectors = list(performance.keys())
        changes = list(performance.values())
        colors = ['red' if change >= 0 else 'green' for change in changes]
        
        fig.add_trace(go.Bar(
            x=sectors,
            y=changes,
            marker_color=colors,
            name="涨跌幅"
        ), row=1, col=2)
    
    fig.update_layout(
        title="行业分析",
        height=500
    )
    
    return fig

def create_valuation_chart(valuation_data: Dict[str, float]) -> go.Figure:
    """创建估值分析图表"""
    metrics = ['PE', 'PB', 'PS', 'PEG']
    current_values = [valuation_data.get(f'{metric.lower()}_current', 0) for metric in metrics]
    industry_avg = [valuation_data.get(f'{metric.lower()}_industry_avg', 0) for metric in metrics]
    
    fig = go.Figure(data=[
        go.Bar(name='当前值', x=metrics, y=current_values),
        go.Bar(name='行业平均', x=metrics, y=industry_avg)
    ])
    
    fig.update_layout(
        title="估值指标对比",
        xaxis_title="估值指标",
        yaxis_title="倍数",
        barmode='group',
        height=400
    )
    
    return fig

def create_trend_analysis_chart(trend_data: Dict[str, Any]) -> go.Figure:
    """创建趋势分析图表"""
    fig = make_subplots(
        rows=2, cols=2,
        subplot_titles=('短期趋势', '中期趋势', '长期趋势', '趋势强度'),
        specs=[[{"type": "indicator"}, {"type": "indicator"}],
               [{"type": "indicator"}, {"type": "bar"}]]
    )
    
    # 短期趋势
    short_trend = trend_data.get('short_term', 0)
    fig.add_trace(go.Indicator(
        mode="gauge+number",
        value=short_trend,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': "短期趋势"},
        gauge={'axis': {'range': [-100, 100]},
               'bar': {'color': "darkblue"},
               'steps': [{'range': [-100, -20], 'color': "lightgray"},
                        {'range': [-20, 20], 'color': "gray"},
                        {'range': [20, 100], 'color': "lightgreen"}],
               'threshold': {'line': {'color': "red", 'width': 4},
                           'thickness': 0.75, 'value': 90}}
    ), row=1, col=1)
    
    # 中期趋势
    medium_trend = trend_data.get('medium_term', 0)
    fig.add_trace(go.Indicator(
        mode="gauge+number",
        value=medium_trend,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': "中期趋势"},
        gauge={'axis': {'range': [-100, 100]},
               'bar': {'color': "darkgreen"}}
    ), row=1, col=2)
    
    # 长期趋势
    long_trend = trend_data.get('long_term', 0)
    fig.add_trace(go.Indicator(
        mode="gauge+number",
        value=long_trend,
        domain={'x': [0, 1], 'y': [0, 1]},
        title={'text': "长期趋势"},
        gauge={'axis': {'range': [-100, 100]},
               'bar': {'color': "darkorange"}}
    ), row=2, col=1)
    
    # 趋势强度
    if 'trend_strength' in trend_data:
        strength_data = trend_data['trend_strength']
        fig.add_trace(go.Bar(
            x=list(strength_data.keys()),
            y=list(strength_data.values()),
            name="趋势强度"
        ), row=2, col=2)
    
    fig.update_layout(
        title="趋势分析",
        height=600
    )
    
    return fig

def create_recommendation_chart(recommendation_data: Dict[str, Any]) -> go.Figure:
    """创建投资建议图表"""
    # 投资建议分布
    recommendations = recommendation_data.get('analyst_recommendations', {})
    
    if not recommendations:
        recommendations = {
            '强烈买入': 2,
            '买入': 5,
            '持有': 3,
            '卖出': 1,
            '强烈卖出': 0
        }
    
    labels = list(recommendations.keys())
    values = list(recommendations.values())
    colors = ['darkgreen', 'green', 'yellow', 'orange', 'red']
    
    fig = go.Figure(data=[go.Pie(
        labels=labels,
        values=values,
        marker_colors=colors,
        hole=0.3
    )])
    
    fig.update_layout(
        title="分析师建议分布",
        height=400
    )
    
    return fig

def create_sentiment_analysis_chart(sentiment_data: Dict[str, float]) -> go.Figure:
    """创建情绪分析图表"""
    sentiments = ['非常悲观', '悲观', '中性', '乐观', '非常乐观']
    values = [
        sentiment_data.get('very_negative', 0),
        sentiment_data.get('negative', 0),
        sentiment_data.get('neutral', 0),
        sentiment_data.get('positive', 0),
        sentiment_data.get('very_positive', 0)
    ]
    
    colors = ['#d62728', '#ff7f0e', '#ffff00', '#2ca02c', '#1f77b4']
    
    fig = go.Figure(data=[
        go.Bar(
            x=sentiments,
            y=values,
            marker_color=colors,
            text=[f'{v:.1f}%' for v in values],
            textposition='auto'
        )
    ])
    
    fig.update_layout(
        title="市场情绪分析",
        xaxis_title="情绪类型",
        yaxis_title="占比 (%)",
        height=400
    )
    
    return fig

def display_analysis_dashboard(analysis_result: Dict[str, Any]):
    """显示分析仪表板"""
    st.subheader("📊 分析仪表板")
    
    # 创建标签页
    tabs = st.tabs(["综合评分", "财务指标", "风险评估", "趋势分析", "投资建议"])
    
    with tabs[0]:
        if 'comprehensive_analysis' in analysis_result:
            fig = create_analysis_summary_chart(analysis_result['comprehensive_analysis'])
            st.plotly_chart(fig, use_container_width=True)
    
    with tabs[1]:
        if 'financial_metrics' in analysis_result:
            fig = create_financial_metrics_chart(analysis_result['financial_metrics'])
            st.plotly_chart(fig, use_container_width=True)
    
    with tabs[2]:
        if 'risk_assessment' in analysis_result:
            fig = create_risk_assessment_chart(analysis_result['risk_assessment'])
            st.plotly_chart(fig, use_container_width=True)
    
    with tabs[3]:
        if 'trend_analysis' in analysis_result:
            fig = create_trend_analysis_chart(analysis_result['trend_analysis'])
            st.plotly_chart(fig, use_container_width=True)
    
    with tabs[4]:
        if 'investment_recommendation' in analysis_result:
            fig = create_recommendation_chart(analysis_result['investment_recommendation'])
            st.plotly_chart(fig, use_container_width=True)

def create_comparison_dashboard(comparison_data: Dict[str, Any]):
    """创建对比分析仪表板"""
    st.subheader("📈 对比分析")
    
    col1, col2 = st.columns(2)
    
    with col1:
        if 'performance_comparison' in comparison_data:
            fig = create_performance_comparison_chart(comparison_data['performance_comparison'])
            st.plotly_chart(fig, use_container_width=True)
    
    with col2:
        if 'valuation_comparison' in comparison_data:
            fig = create_valuation_chart(comparison_data['valuation_comparison'])
            st.plotly_chart(fig, use_container_width=True)
