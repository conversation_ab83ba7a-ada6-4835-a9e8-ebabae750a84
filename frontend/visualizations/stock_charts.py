"""
股票图表可视化
"""
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta

def create_candlestick_chart(data: pd.DataFrame, title: str = "K线图") -> go.Figure:
    """创建K线图"""
    fig = go.Figure(data=go.Candlestick(
        x=data.index,
        open=data['open'],
        high=data['high'],
        low=data['low'],
        close=data['close'],
        name="K线"
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="价格",
        xaxis_rangeslider_visible=False,
        height=500
    )
    
    return fig

def create_volume_chart(data: pd.DataFrame, title: str = "成交量") -> go.Figure:
    """创建成交量图"""
    colors = ['red' if close >= open else 'green' 
              for close, open in zip(data['close'], data['open'])]
    
    fig = go.Figure(data=go.Bar(
        x=data.index,
        y=data['volume'],
        marker_color=colors,
        name="成交量"
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="成交量",
        height=300
    )
    
    return fig

def create_price_line_chart(data: pd.DataFrame, columns: List[str], title: str = "价格走势") -> go.Figure:
    """创建价格线图"""
    fig = go.Figure()
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    for i, col in enumerate(columns):
        if col in data.columns:
            fig.add_trace(go.Scatter(
                x=data.index,
                y=data[col],
                mode='lines',
                name=col,
                line=dict(color=colors[i % len(colors)])
            ))
    
    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="价格",
        height=400,
        hovermode='x unified'
    )
    
    return fig

def create_technical_indicators_chart(data: pd.DataFrame, indicators: Dict[str, Any]) -> go.Figure:
    """创建技术指标图"""
    fig = make_subplots(
        rows=3, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.05,
        subplot_titles=('价格与移动平均线', 'MACD', 'RSI'),
        row_heights=[0.5, 0.25, 0.25]
    )
    
    # 价格和移动平均线
    fig.add_trace(go.Scatter(
        x=data.index, y=data['close'],
        mode='lines', name='收盘价',
        line=dict(color='blue')
    ), row=1, col=1)
    
    if 'ma5' in data.columns:
        fig.add_trace(go.Scatter(
            x=data.index, y=data['ma5'],
            mode='lines', name='MA5',
            line=dict(color='orange')
        ), row=1, col=1)
    
    if 'ma20' in data.columns:
        fig.add_trace(go.Scatter(
            x=data.index, y=data['ma20'],
            mode='lines', name='MA20',
            line=dict(color='red')
        ), row=1, col=1)
    
    # MACD
    if 'macd' in data.columns:
        fig.add_trace(go.Scatter(
            x=data.index, y=data['macd'],
            mode='lines', name='MACD',
            line=dict(color='blue')
        ), row=2, col=1)
    
    if 'macd_signal' in data.columns:
        fig.add_trace(go.Scatter(
            x=data.index, y=data['macd_signal'],
            mode='lines', name='Signal',
            line=dict(color='red')
        ), row=2, col=1)
    
    if 'macd_histogram' in data.columns:
        fig.add_trace(go.Bar(
            x=data.index, y=data['macd_histogram'],
            name='Histogram',
            marker_color='gray'
        ), row=2, col=1)
    
    # RSI
    if 'rsi' in data.columns:
        fig.add_trace(go.Scatter(
            x=data.index, y=data['rsi'],
            mode='lines', name='RSI',
            line=dict(color='purple')
        ), row=3, col=1)
        
        # RSI超买超卖线
        fig.add_hline(y=70, line_dash="dash", line_color="red", row=3, col=1)
        fig.add_hline(y=30, line_dash="dash", line_color="green", row=3, col=1)
    
    fig.update_layout(
        height=800,
        title="技术指标分析",
        showlegend=True
    )
    
    return fig

def create_comparison_chart(data: Dict[str, pd.DataFrame], title: str = "股票对比") -> go.Figure:
    """创建股票对比图"""
    fig = go.Figure()
    
    colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']
    
    for i, (stock_name, stock_data) in enumerate(data.items()):
        # 计算相对涨跌幅
        normalized_data = (stock_data['close'] / stock_data['close'].iloc[0] - 1) * 100
        
        fig.add_trace(go.Scatter(
            x=stock_data.index,
            y=normalized_data,
            mode='lines',
            name=stock_name,
            line=dict(color=colors[i % len(colors)])
        ))
    
    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="相对涨跌幅 (%)",
        height=500,
        hovermode='x unified'
    )
    
    return fig

def create_correlation_heatmap(correlation_matrix: pd.DataFrame, title: str = "相关性热力图") -> go.Figure:
    """创建相关性热力图"""
    fig = go.Figure(data=go.Heatmap(
        z=correlation_matrix.values,
        x=correlation_matrix.columns,
        y=correlation_matrix.index,
        colorscale='RdBu',
        zmid=0,
        text=correlation_matrix.values,
        texttemplate="%{text:.2f}",
        textfont={"size": 10},
        hoverongaps=False
    ))
    
    fig.update_layout(
        title=title,
        height=500
    )
    
    return fig

def create_sector_pie_chart(sector_data: Dict[str, float], title: str = "行业分布") -> go.Figure:
    """创建行业分布饼图"""
    fig = go.Figure(data=[go.Pie(
        labels=list(sector_data.keys()),
        values=list(sector_data.values()),
        hole=0.3
    )])
    
    fig.update_layout(
        title=title,
        height=400
    )
    
    return fig

def create_risk_return_scatter(risk_return_data: pd.DataFrame, title: str = "风险收益散点图") -> go.Figure:
    """创建风险收益散点图"""
    fig = go.Figure(data=go.Scatter(
        x=risk_return_data['risk'],
        y=risk_return_data['return'],
        mode='markers+text',
        text=risk_return_data.index,
        textposition="top center",
        marker=dict(
            size=10,
            color=risk_return_data['return'],
            colorscale='RdYlGn',
            showscale=True,
            colorbar=dict(title="收益率")
        )
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title="风险 (标准差)",
        yaxis_title="收益率 (%)",
        height=500
    )
    
    return fig

def create_volume_price_analysis(data: pd.DataFrame, title: str = "量价分析") -> go.Figure:
    """创建量价分析图"""
    fig = make_subplots(
        rows=2, cols=1,
        shared_xaxes=True,
        vertical_spacing=0.1,
        subplot_titles=('价格', '成交量'),
        row_heights=[0.7, 0.3]
    )
    
    # 价格图
    fig.add_trace(go.Scatter(
        x=data.index,
        y=data['close'],
        mode='lines',
        name='收盘价',
        line=dict(color='blue')
    ), row=1, col=1)
    
    # 成交量图
    colors = ['red' if close >= open else 'green' 
              for close, open in zip(data['close'], data['open'])]
    
    fig.add_trace(go.Bar(
        x=data.index,
        y=data['volume'],
        marker_color=colors,
        name='成交量',
        opacity=0.7
    ), row=2, col=1)
    
    fig.update_layout(
        title=title,
        height=600,
        showlegend=True
    )
    
    return fig

def create_bollinger_bands_chart(data: pd.DataFrame, title: str = "布林带") -> go.Figure:
    """创建布林带图"""
    fig = go.Figure()
    
    # 上轨
    fig.add_trace(go.Scatter(
        x=data.index,
        y=data['bb_upper'],
        mode='lines',
        name='上轨',
        line=dict(color='red', dash='dash')
    ))
    
    # 中轨（移动平均线）
    fig.add_trace(go.Scatter(
        x=data.index,
        y=data['bb_middle'],
        mode='lines',
        name='中轨',
        line=dict(color='blue')
    ))
    
    # 下轨
    fig.add_trace(go.Scatter(
        x=data.index,
        y=data['bb_lower'],
        mode='lines',
        name='下轨',
        line=dict(color='green', dash='dash'),
        fill='tonexty',
        fillcolor='rgba(0,100,80,0.1)'
    ))
    
    # 价格
    fig.add_trace(go.Scatter(
        x=data.index,
        y=data['close'],
        mode='lines',
        name='收盘价',
        line=dict(color='black', width=2)
    ))
    
    fig.update_layout(
        title=title,
        xaxis_title="日期",
        yaxis_title="价格",
        height=500,
        hovermode='x unified'
    )
    
    return fig

def display_chart(fig: go.Figure, use_container_width: bool = True):
    """显示图表"""
    st.plotly_chart(fig, use_container_width=use_container_width)

def create_metrics_dashboard(metrics: Dict[str, Any]):
    """创建指标仪表板"""
    col1, col2, col3, col4 = st.columns(4)
    
    with col1:
        st.metric(
            "当前价格",
            f"¥{metrics.get('current_price', 0):.2f}",
            f"{metrics.get('price_change', 0):+.2f}"
        )
    
    with col2:
        st.metric(
            "涨跌幅",
            f"{metrics.get('change_percent', 0):.2f}%",
            f"{metrics.get('change_percent', 0):+.2f}%"
        )
    
    with col3:
        st.metric(
            "成交量",
            f"{metrics.get('volume', 0):,.0f}",
            f"{metrics.get('volume_change', 0):+.1f}%"
        )
    
    with col4:
        st.metric(
            "市值",
            f"{metrics.get('market_cap', 0):.1f}亿",
            help="总市值"
        )
