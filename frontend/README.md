# 股票分析RAG系统 - Streamlit前端界面

## 📖 概述

基于Streamlit构建的股票分析RAG系统前端界面，提供直观的用户交互体验，支持Text2SQL查询和RAG增强的股票分析功能。

## 🚀 快速开始

### 启动前端应用

```bash
# 方法1：使用启动脚本
python start_streamlit.py

# 方法2：直接使用streamlit命令
streamlit run frontend/main.py --server.port 8501
```

### 访问地址

- **主界面**: http://localhost:8501
- **Text2SQL查询**: http://localhost:8501/text2sql
- **股票分析**: http://localhost:8501/stock_analysis

## 📁 项目结构

```
frontend/
├── main.py                 # Streamlit主应用入口
├── pages/                  # 页面模块
│   ├── text2sql.py        # Text2SQL查询页面
│   └── stock_analysis.py  # 股票分析页面
├── components/            # UI组件
│   └── common.py         # 通用组件
├── visualizations/       # 数据可视化
│   ├── stock_charts.py   # 股票图表
│   └── analysis_charts.py # 分析图表
└── README.md             # 说明文档
```

## 🎯 主要功能

### 1. 系统概览页面

- **系统状态监控**: 实时显示MySQL、ChromaDB、Ollama服务状态
- **快速导航**: 一键跳转到各功能模块
- **系统信息**: 显示环境配置和版本信息

### 2. Text2SQL查询页面

#### 核心功能
- **自然语言查询**: 输入中文查询，自动生成SQL
- **多格式输出**: 支持表格、JSON、CSV格式
- **查询解释**: 详细解释查询意图和生成的SQL
- **查询历史**: 保存和管理历史查询记录

#### 使用示例
```
输入: "查询平安银行的基本信息"
输出: 自动生成SQL并执行，返回结果表格
```

#### 查询建议
- 查询平安银行的基本信息
- 显示市值最大的10只股票
- 查找今日涨幅超过5%的股票
- 显示银行板块的股票列表
- 查询贵州茅台的历史价格

### 3. 股票分析页面

#### 分析类型
- **基本面分析**: 财务指标、盈利能力、成长性分析
- **技术面分析**: 技术指标、趋势分析、支撑阻力位
- **资金面分析**: 资金流向、成交量分析
- **行业对比**: 行业内股票对比分析
- **风险评估**: 风险等级和风险因素评估

#### 可视化图表
- 综合分析雷达图
- 技术指标图表
- K线图和成交量图
- 财务指标对比图
- 风险评估图表

## 🎨 界面特性

### 响应式设计
- 支持桌面和移动设备
- 自适应布局和组件大小
- 优化的用户体验

### 主题配置
- 专业的蓝色主题
- 清晰的视觉层次
- 一致的设计语言

### 交互体验
- 实时数据更新
- 加载状态提示
- 错误处理和用户反馈
- 数据导出功能

## ⚙️ 配置说明

### Streamlit配置文件

配置文件位置: `.streamlit/config.toml`

```toml
[server]
port = 8501
address = "0.0.0.0"
enableCORS = true
enableXsrfProtection = true

[theme]
primaryColor = "#1f77b4"
backgroundColor = "#ffffff"
secondaryBackgroundColor = "#f0f2f6"
textColor = "#262730"
```

### 环境变量

```bash
STREAMLIT_SERVER_PORT=8501
STREAMLIT_SERVER_ADDRESS=0.0.0.0
STREAMLIT_BROWSER_GATHER_USAGE_STATS=false
```

## 🔧 开发指南

### 添加新页面

1. 在 `frontend/pages/` 目录下创建新的Python文件
2. 实现页面逻辑和UI组件
3. 在主应用中添加导航链接

### 创建自定义组件

1. 在 `frontend/components/` 目录下添加组件文件
2. 实现可复用的UI组件函数
3. 在页面中导入和使用组件

### 添加数据可视化

1. 在 `frontend/visualizations/` 目录下添加图表文件
2. 使用Plotly创建交互式图表
3. 在分析页面中集成图表

## 🐛 故障排除

### 常见问题

1. **端口占用**
   ```bash
   # 检查端口占用
   lsof -i :8501
   # 杀死占用进程
   kill -9 <PID>
   ```

2. **模块导入错误**
   ```bash
   # 确保在项目根目录运行
   cd /path/to/llm-rag
   python start_streamlit.py
   ```

3. **API连接失败**
   - 确保FastAPI服务正在运行 (端口8000)
   - 检查防火墙设置
   - 验证API端点配置

### 日志查看

Streamlit日志会显示在终端中，包括：
- 系统启动信息
- 错误和警告信息
- API请求日志

## 📊 性能优化

### 缓存策略
- 使用 `@st.cache_data` 缓存数据查询结果
- 使用 `@st.cache_resource` 缓存资源连接
- 合理设置缓存过期时间

### 数据加载优化
- 分页加载大量数据
- 异步加载非关键数据
- 使用进度条显示加载状态

## 🔒 安全考虑

- 输入验证和SQL注入防护
- CSRF保护已启用
- 敏感信息不在前端显示
- API调用使用安全的HTTP方法

## 📝 更新日志

### v1.0.0 (2024-12-29)
- ✅ 完成Streamlit前端界面开发
- ✅ 实现Text2SQL查询页面
- ✅ 实现股票分析页面
- ✅ 添加数据可视化组件
- ✅ 完成系统状态监控
- ✅ 优化用户体验和界面设计

## 🤝 贡献指南

1. Fork项目仓库
2. 创建功能分支
3. 提交代码更改
4. 创建Pull Request
5. 等待代码审查

## 📞 技术支持

如有问题或建议，请通过以下方式联系：
- 创建GitHub Issue
- 发送邮件至项目维护者
- 查看项目文档和FAQ
