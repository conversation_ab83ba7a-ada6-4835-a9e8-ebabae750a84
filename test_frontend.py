#!/usr/bin/env python3
"""
前端功能测试脚本
"""
import requests
import time
import sys
from pathlib import Path

def test_streamlit_app():
    """测试Streamlit应用是否正常运行"""
    print("🧪 测试Streamlit应用...")
    
    try:
        # 测试主页面
        response = requests.get("http://localhost:8501", timeout=10)
        if response.status_code == 200:
            print("✅ Streamlit主页面访问正常")
        else:
            print(f"❌ Streamlit主页面访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Streamlit应用连接失败: {e}")
        print("请确保Streamlit应用正在运行: python start_streamlit.py")
        return False
    
    return True

def test_api_service():
    """测试API服务是否正常运行"""
    print("🧪 测试API服务...")
    
    try:
        # 测试健康检查
        response = requests.get("http://localhost:8000/health", timeout=10)
        if response.status_code == 200:
            print("✅ API服务健康检查正常")
        else:
            print(f"❌ API服务健康检查失败: {response.status_code}")
            return False
        
        # 测试系统状态
        response = requests.get("http://localhost:8000/api/v1/system/status", timeout=10)
        if response.status_code == 200:
            status_data = response.json()
            print(f"✅ 系统状态: {status_data.get('message', 'OK')}")
        else:
            print(f"❌ 系统状态检查失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ API服务连接失败: {e}")
        print("请确保API服务正在运行: python start_server.py")
        return False
    
    return True

def test_text2sql_api():
    """测试Text2SQL API"""
    print("🧪 测试Text2SQL API...")
    
    try:
        # 测试查询解释
        response = requests.post(
            "http://localhost:8000/api/v1/text2sql/explain",
            params={"natural_query": "查询平安银行的基本信息"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ Text2SQL查询解释功能正常")
            else:
                print(f"❌ Text2SQL查询解释失败: {result.get('message')}")
                return False
        else:
            print(f"❌ Text2SQL API请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Text2SQL API连接失败: {e}")
        return False
    
    return True

def test_analysis_api():
    """测试股票分析API"""
    print("🧪 测试股票分析API...")
    
    try:
        # 测试股票概览
        response = requests.get(
            "http://localhost:8000/api/v1/analysis/overview",
            params={"stock_input": "000001"},
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get("success"):
                print("✅ 股票分析概览功能正常")
            else:
                print(f"❌ 股票分析概览失败: {result.get('message')}")
                return False
        else:
            print(f"❌ 股票分析API请求失败: {response.status_code}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 股票分析API连接失败: {e}")
        return False
    
    return True

def check_file_structure():
    """检查前端文件结构"""
    print("🧪 检查前端文件结构...")
    
    required_files = [
        "frontend/main.py",
        "frontend/pages/text2sql.py",
        "frontend/pages/stock_analysis.py",
        "frontend/components/common.py",
        "frontend/visualizations/stock_charts.py",
        "frontend/visualizations/analysis_charts.py",
        "start_streamlit.py",
        "start_full_system.py",
        ".streamlit/config.toml"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)
    
    if missing_files:
        print(f"❌ 缺少文件: {', '.join(missing_files)}")
        return False
    
    print("✅ 前端文件结构完整")
    return True

def run_comprehensive_test():
    """运行综合测试"""
    print("=" * 60)
    print("🧪 股票分析RAG系统前端测试")
    print("=" * 60)
    
    tests = [
        ("文件结构检查", check_file_structure),
        ("API服务测试", test_api_service),
        ("Text2SQL API测试", test_text2sql_api),
        ("股票分析API测试", test_analysis_api),
        ("Streamlit应用测试", test_streamlit_app),
    ]
    
    passed_tests = 0
    total_tests = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}...")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {passed_tests}/{total_tests} 通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！前端系统运行正常")
        print("\n💡 使用指南:")
        print("  - 访问 http://localhost:8501 使用Streamlit前端")
        print("  - 访问 http://localhost:8000/docs 查看API文档")
    else:
        print("⚠️  部分测试失败，请检查系统配置")
        print("\n🔧 故障排除:")
        print("  1. 确保所有依赖已安装: pip install -r requirements.txt")
        print("  2. 启动完整系统: python start_full_system.py")
        print("  3. 检查端口占用: lsof -i :8000 && lsof -i :8501")
    
    print("=" * 60)
    
    return passed_tests == total_tests

def main():
    """主函数"""
    if len(sys.argv) > 1:
        test_type = sys.argv[1].lower()
        
        if test_type == "structure":
            check_file_structure()
        elif test_type == "api":
            test_api_service()
        elif test_type == "text2sql":
            test_text2sql_api()
        elif test_type == "analysis":
            test_analysis_api()
        elif test_type == "streamlit":
            test_streamlit_app()
        else:
            print("❌ 未知的测试类型")
            print("可用的测试类型: structure, api, text2sql, analysis, streamlit")
    else:
        # 运行综合测试
        success = run_comprehensive_test()
        sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
