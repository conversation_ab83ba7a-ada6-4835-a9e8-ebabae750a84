#!/usr/bin/env python3
"""
测试MySQL连接
"""
import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.settings import settings
from database.mysql_client import mysql_client
from utils.logger import get_logger

logger = get_logger("test_mysql")

def test_mysql_connection():
    """测试MySQL连接"""
    print("=" * 50)
    print("🧪 MySQL连接测试")
    print("=" * 50)
    
    # 显示配置信息
    print(f"📋 连接配置:")
    print(f"  - 主机: {settings.mysql_host}")
    print(f"  - 端口: {settings.mysql_port}")
    print(f"  - 用户: {settings.mysql_user}")
    print(f"  - 数据库: {settings.mysql_database}")
    print(f"  - 连接URL: {settings.mysql_url}")
    print()
    
    try:
        # 测试初始化
        print("🔧 正在初始化MySQL客户端...")
        mysql_client.initialize()
        print("✅ MySQL客户端初始化成功")
        
        # 测试连接
        print("🔗 正在测试数据库连接...")
        connection_ok = mysql_client.test_connection()
        
        if connection_ok:
            print("✅ MySQL数据库连接测试成功")
        else:
            print("❌ MySQL数据库连接测试失败")
            return False
        
        # 测试查询
        print("📊 正在测试查询功能...")
        result = mysql_client.execute_query("SELECT DATABASE() as current_db, VERSION() as version")
        
        if result:
            print("✅ 查询测试成功")
            for row in result:
                print(f"  - 当前数据库: {row.get('current_db')}")
                print(f"  - MySQL版本: {row.get('version')}")
        else:
            print("❌ 查询测试失败")
            return False
        
        # 测试表查询
        print("📋 正在测试表结构查询...")
        tables = mysql_client.execute_query("SHOW TABLES")
        
        if tables:
            print(f"✅ 找到 {len(tables)} 个表:")
            for i, table in enumerate(tables[:5]):  # 只显示前5个表
                table_name = list(table.values())[0]
                print(f"  {i+1}. {table_name}")
            if len(tables) > 5:
                print(f"  ... 还有 {len(tables) - 5} 个表")
        else:
            print("⚠️  没有找到表")
        
        print("\n🎉 所有测试通过！MySQL连接正常工作")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        logger.error(f"MySQL连接测试失败: {e}")
        return False

def test_specific_query():
    """测试特定查询"""
    print("\n" + "=" * 50)
    print("🔍 特定查询测试")
    print("=" * 50)
    
    try:
        # 测试股票基本信息查询
        print("📊 查询股票基本信息...")
        result = mysql_client.execute_query(
            "SELECT ts_code, name, industry, market FROM stock_basic WHERE ts_code = '000001.SZ' LIMIT 1"
        )
        
        if result:
            print("✅ 股票基本信息查询成功")
            for row in result:
                print(f"  - 股票代码: {row.get('ts_code')}")
                print(f"  - 股票名称: {row.get('name')}")
                print(f"  - 所属行业: {row.get('industry')}")
                print(f"  - 市场: {row.get('market')}")
        else:
            print("⚠️  没有找到股票数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 特定查询测试失败: {e}")
        return False

if __name__ == "__main__":
    success = test_mysql_connection()
    
    if success:
        test_specific_query()
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 MySQL连接测试完成，一切正常！")
    else:
        print("❌ MySQL连接测试失败，请检查配置")
    print("=" * 50)
