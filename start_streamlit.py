#!/usr/bin/env python3
"""
启动Streamlit前端应用
"""
import os
import sys
import subprocess
from pathlib import Path

def main():
    """启动Streamlit应用"""
    # 设置环境变量
    os.environ["STREAMLIT_SERVER_PORT"] = "8501"
    os.environ["STREAMLIT_SERVER_ADDRESS"] = "0.0.0.0"
    os.environ["STREAMLIT_BROWSER_GATHER_USAGE_STATS"] = "false"
    
    # 获取项目根目录
    project_root = Path(__file__).parent
    frontend_main = project_root / "frontend" / "main.py"
    
    if not frontend_main.exists():
        print("❌ 前端主文件不存在:", frontend_main)
        sys.exit(1)
    
    print("🚀 启动Streamlit前端应用...")
    print(f"📁 项目目录: {project_root}")
    print(f"🌐 访问地址: http://localhost:8501")
    print("=" * 50)
    
    try:
        # 启动Streamlit
        cmd = [
            sys.executable, "-m", "streamlit", "run",
            str(frontend_main),
            "--server.port", "8501",
            "--server.address", "0.0.0.0",
            "--browser.gatherUsageStats", "false",
            "--theme.primaryColor", "#1f77b4",
            "--theme.backgroundColor", "#ffffff",
            "--theme.secondaryBackgroundColor", "#f0f2f6",
            "--theme.textColor", "#262730"
        ]
        
        subprocess.run(cmd, cwd=project_root)
        
    except KeyboardInterrupt:
        print("\n👋 Streamlit应用已停止")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
